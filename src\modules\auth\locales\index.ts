import enTranslation from './en.json';
import viTranslation from './vi.json';
import zhTranslation from './zh.json';

export const authResources = {
  en: {
    auth: enTranslation.auth,
    validation: enTranslation.validation,
  },
  vi: {
    auth: viTranslation.auth,
    validation: viTranslation.validation,
  },
  zh: {
    auth: zhTranslation.auth,
    validation: zhTranslation.validation,
  },
};

export default authResources;
