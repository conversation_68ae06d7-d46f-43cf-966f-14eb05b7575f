import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Card, Typography, ResponsiveImage, IconCard } from '@/shared/components/common';
import ForgotPasswordForm from '../components/ForgotPasswordForm';
// Import logo
import logoImage from '@/shared/assets/images/logo/logo.png';

/**
 * Forgot password page component
 */
const ForgotPasswordPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Card variant="elevated" className="w-full max-w-md">
      <div className="flex flex-col items-center mb-">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-10">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-8 object-contain max-w-[120px]"
          />
        </div>
      </div>

      <div className="space-y-6">
        <div className="text-center">
          <Typography variant="h4" className="mb-2">
            {t('auth.resetPassword')}
          </Typography>
          <Typography variant="body2" color="muted">
            {t('auth.forgotPasswordDescription')}
          </Typography>
        </div>

        <ForgotPasswordForm />

        <div className="text-center">
          <Typography variant="body2" color="muted">
            {t('auth.rememberPassword')}{' '}
            <Link to="/auth">
              <IconCard
                icon="arrow-left"
                title={t('auth.backToLogin', 'Quay lại đăng nhập')}
                className="cursor-pointer"
              />
            </Link>
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default ForgotPasswordPage;
