{"auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "fullName": "Full Name", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "emailOrPhone": "Email or Phone Number", "rememberMe": "Remember Me", "signIn": "Sign In", "signUp": "Sign Up", "orContinueWith": "Or continue with", "loginWithGoogle": "Login with Google", "loginWithFacebook": "Login with Facebook", "loginWithZalo": "Login with <PERSON><PERSON>", "forgotPasswordDescription": "Enter your email address and we'll send you an OTP code to reset your password.", "forgotPasswordError": "An error occurred while requesting password reset", "newPassword": "New Password", "newPasswordDescription": "Enter your new password", "resetPasswordDescription": "Enter the OTP code sent to your email", "resetPasswordSuccess": "Password reset successful. Please login with your new password.", "resetPasswordError": "Password reset failed", "verifyOtp": "Verify OTP", "resendOtpError": "Failed to resend OTP", "loginError": "<PERSON><PERSON> failed. Please check your credentials and try again.", "registerError": "Registration failed. Please try again later.", "recaptchaError": "Please verify that you are not a robot.", "admin": {"login": "<PERSON><PERSON>", "signIn": "Sign In to Admin", "forgotPassword": "Admin Forgot Password", "resetPassword": "Admin Reset Password", "verifyPasswordReset": "Verify Admin Password Reset", "accessMessage": "Access to admin dashboard and management tools", "loginError": "Admin login failed. Please check your credentials and try again.", "forgotPasswordDescription": "Enter your admin email address and we'll send you an OTP code to reset your password.", "forgotPasswordError": "An error occurred while requesting admin password reset"}, "verifyAccount": "Verify Account", "verifyEmailDescription": "We've sent a verification code to your email {{email}}. Please check your inbox.", "verifySmsDescription": "We've sent a verification code to your phone {{phone}}. Please check your messages.", "verifyDescription": "We've sent a verification code. Please check your email or messages.", "resendCode": "Resend Code", "codeSent": "Verification code has been sent.", "resendFailed": "Failed to resend verification code. Please try again.", "backToLogin": "Back to Login", "twoFactorAuth": "Two-Factor Authentication", "twoFactorAuthDescription": "Please enter the verification code to continue.", "verificationCode": "Verification Code", "verify": "Verify", "didntReceiveCode": "Didn't receive the code?", "passwordRequirements": "Password must be at least 8 characters long, including uppercase, lowercase, numbers, and special characters.", "enterOtp": "Enter 6-digit verification code", "verifySuccess": "Verification Successful", "redirecting": "Redirecting to home page...", "invalidOtp": "Invalid OTP. Please enter all 6 digits.", "verifyOtpError": "OTP verification failed", "otpExpired": "OTP has expired", "otpExpiredMessage": "OTP has expired. Please request a new code.", "otpExpiresIn": {"prefix": "OTP expires in:", "full": "OTP expires in: {{time}}"}, "time": {"days": "d", "hours": "h", "minutes": "m", "seconds": "s", "expired": "OTP has expired"}}, "validation": {"required": "{{field}} is required", "email": "Invalid email address", "minLength": "{{field}} must be at least {{length}} characters", "maxLength": "{{field}} must not exceed {{length}} characters", "passwordUppercase": "Password must contain at least one uppercase letter", "passwordLowercase": "Password must contain at least one lowercase letter", "passwordNumber": "Password must contain at least one number", "passwordSpecial": "Password must contain at least one special character", "passwordsMatch": "Passwords do not match"}, "header": {"title": "RedAI Admin"}}