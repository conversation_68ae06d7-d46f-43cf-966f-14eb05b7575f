import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  ResponsiveImage,
  Alert,
  OTPInput,
} from '@/shared/components/common';
import { z } from 'zod';
import { useAuthCommon } from '@/shared/hooks';
import { useVerifyForgotPassword, useResetPassword, useResendOtp } from '../hooks/useAuthQuery';
import CountdownTimer from '../components/CountdownTimer';
import logoImage from '@/shared/assets/images/logo/logo.png';

/**
 * Verify forgot password page component
 */
const VerifyForgotPasswordPage: React.FC = () => {
  const { t } = useTranslation(['auth', 'authValidation']);
  const navigate = useNavigate();
  const { verifyToken, verifyExpiresAt } = useAuthCommon();
  const [otp, setOtp] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isOtpExpired, setIsOtpExpired] = useState<boolean>(false);
  const [isOtpVerified, setIsOtpVerified] = useState<boolean>(false);
  const [changePasswordToken, setChangePasswordToken] = useState<string | null>(null);

  // Sử dụng hook để xác thực OTP quên mật khẩu
  const { mutate: verifyForgotPassword, isPending: isVerifying } = useVerifyForgotPassword();

  // Sử dụng hook để đặt lại mật khẩu
  const { mutate: resetPassword, isPending: isResetting } = useResetPassword();

  // Sử dụng hook để gửi lại OTP
  const { mutate: resendOtp, isPending: isResending } = useResendOtp();

  // Kiểm tra nếu không có verifyToken, chuyển hướng về trang quên mật khẩu
  useEffect(() => {
    if (!verifyToken) {
      navigate('/auth/forgot-password');
    }
  }, [verifyToken, navigate]);

  // Kiểm tra thời gian hết hạn của OTP
  useEffect(() => {
    if (verifyExpiresAt) {
      console.log('verifyExpiresAt:', verifyExpiresAt, 'Current Time:', Date.now());
      const checkExpiration = () => {
        const now = Date.now();
        if (now >= verifyExpiresAt) {
          setIsOtpExpired(true);
        } else {
          setIsOtpExpired(false);
        }
      };

      // Kiểm tra ngay lập tức
      checkExpiration();

      // Kiểm tra mỗi giây
      const timer = setInterval(checkExpiration, 1000);

      return () => clearInterval(timer);
    }
    return undefined;
  }, [verifyExpiresAt]);

  // Không cần schema cho OTP vì chúng ta sử dụng component OTPInput

  // Schema cho form đặt lại mật khẩu
  const passwordSchema = z
    .object({
      newPassword: z
        .string()
        .min(1, t('authValidation:required', { field: t('auth:newPassword') }))
        .min(8, t('authValidation:minLength', { field: t('auth:newPassword'), length: 8 }))
        .regex(/[A-Z]/, t('authValidation:passwordUppercase'))
        .regex(/[a-z]/, t('authValidation:passwordLowercase'))
        .regex(/[0-9]/, t('authValidation:passwordNumber'))
        .regex(/[^A-Za-z0-9]/, t('authValidation:passwordSpecial')),
      confirmPassword: z
        .string()
        .min(1, t('authValidation:required', { field: t('auth:confirmPassword') })),
    })
    .refine(data => data.newPassword === data.confirmPassword, {
      message: t('authValidation:passwordsMatch'),
      path: ['confirmPassword'],
    });

  // Hàm xác thực OTP
  const handleVerifyOtp = () => {
    if (!verifyToken) return;
    if (!otp || otp.length !== 6) {
      setError(t('auth:invalidOtp'));
      return;
    }

    setError(null);

    // Gọi API xác thực OTP quên mật khẩu
    verifyForgotPassword(
      {
        otpToken: verifyToken,
        otp: otp,
      },
      {
        onSuccess: response => {
          if (response.code === 200) {
            // Lưu token đổi mật khẩu
            setChangePasswordToken(response.result.changePasswordToken);
            setIsOtpVerified(true);
          } else {
            setError(response.message || t('auth:verifyOtpError'));
          }
        },
        onError: error => {
          console.error('Verify OTP error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth:verifyOtpError');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setError(errorMsg);
        },
      }
    );
  };

  // Hàm gửi lại mã OTP
  const handleResendOtp = () => {
    if (!verifyToken) return;
    setError(null);

    // Gọi API gửi lại OTP
    resendOtp(
      {
        otpToken: verifyToken,
      },
      {
        onSuccess: response => {
          if (response.code === 200) {
            // Cập nhật thông tin xác thực trong Redux store
            // Lưu ý: Cần cập nhật lại verifyToken và expiresAt từ response
            // Điều này sẽ được xử lý bởi component cha
          } else {
            setError(response.message || t('auth:resendOtpError'));
          }
        },
        onError: error => {
          console.error('Resend OTP error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth:resendOtpError');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setError(errorMsg);
        },
      }
    );
  };

  // Hàm đặt lại mật khẩu
  const handleResetPassword = (values: unknown) => {
    if (!changePasswordToken) return;

    // Use type assertion with a specific type instead of 'any'
    const passwordValues = values as z.infer<typeof passwordSchema>;

    setError(null);

    // Gọi API đặt lại mật khẩu
    resetPassword(
      {
        token: changePasswordToken,
        password: passwordValues.newPassword,
        confirmPassword: passwordValues.confirmPassword,
      },
      {
        onSuccess: response => {
          if (response.code === 200) {
            // Chuyển hướng về trang đăng nhập với thông báo thành công
            navigate('/auth', { state: { message: t('auth:resetPasswordSuccess') } });
          } else {
            setError(response.message || t('auth:resetPasswordError'));
          }
        },
        onError: error => {
          console.error('Reset password error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth:resetPasswordError');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setError(errorMsg);
        },
      }
    );
  };

  return (
    <Card variant="elevated" className="w-full max-w-md">
      <div className="flex flex-col items-center mb-8">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-12">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-full object-contain max-w-[50%]"
          />
        </div>
      </div>

      <div className="space-y-6">
        <div className="text-center">
          <Typography variant="h4" className="mb-2">
            {t('auth:resetPassword')}
          </Typography>
          <Typography variant="body2" color="muted">
            {isOtpVerified ? t('auth:newPasswordDescription') : t('auth:resetPasswordDescription')}
          </Typography>
        </div>

        {error && <Alert type="error" message={error} className="mb-4" />}

        {/* Hiển thị đồng hồ đếm ngược nếu chưa xác thực OTP */}
        {!isOtpVerified && verifyExpiresAt && (
          <CountdownTimer
            expiresAt={verifyExpiresAt}
            onExpire={() => setIsOtpExpired(true)}
            className="mb-4 text-center"
          />
        )}

        {/* Form nhập OTP */}
        {!isOtpVerified && (
          <div className="space-y-6">
            <OTPInput
              length={6}
              onChange={setOtp}
              onComplete={handleVerifyOtp}
              disabled={isOtpExpired}
              className="mb-4"
            />

            <Button
              type="button"
              variant="primary"
              fullWidth
              onClick={handleVerifyOtp}
              isLoading={isVerifying}
              disabled={otp.length !== 6 || isOtpExpired}
            >
              {t('auth:verifyOtp')}
            </Button>

            <div className="text-center mt-4">
              <Typography variant="body2" color="muted">
                {t('auth:didntReceiveCode')}{' '}
                <a
                  href="#"
                  onClick={e => {
                    e.preventDefault();
                    handleResendOtp();
                  }}
                  className={`text-primary hover:text-primary-dark transition-colors ${isResending || !isOtpExpired ? 'pointer-events-none opacity-50' : ''}`}
                >
                  {t('auth:resendCode')}
                </a>
              </Typography>
            </div>
          </div>
        )}

        {/* Form đặt lại mật khẩu */}
        {isOtpVerified && (
          <Form schema={passwordSchema} onSubmit={handleResetPassword} className="space-y-4">
            <FormItem
              name="newPassword"
              label={t('auth:newPassword')}
              required
              helpText={t('auth:passwordRequirements')}
            >
              <Input
                type="password"
                placeholder="••••••••"
                leftIcon={<Icon name="lock" size="sm" />}
                fullWidth
              />
            </FormItem>

            <FormItem name="confirmPassword" label={t('auth:confirmPassword')} required>
              <Input
                type="password"
                placeholder="••••••••"
                leftIcon={<Icon name="lock" size="sm" />}
                fullWidth
              />
            </FormItem>

            <Button type="submit" variant="primary" fullWidth isLoading={isResetting}>
              {t('auth:resetPassword')}
            </Button>
          </Form>
        )}

        <div className="text-center">
          <Typography variant="body2" color="muted" className="mt-2">
            <Link to="/auth" className="text-primary hover:text-primary-dark transition-colors">
              {t('auth:backToLogin')}
            </Link>
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default VerifyForgotPasswordPage;
