import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Form,
  FormItem,
  ResponsiveImage,
  Alert,
  OTPInput,
  PasswordInput,
} from '@/shared/components/common';
import { z } from 'zod';
import { useAuthCommon } from '@/shared/hooks';
import { useVerifyForgotPassword, useResetPassword, useResendOtp } from '../hooks/useAuthQuery';
import CountdownTimer from '../components/CountdownTimer';
import { User } from '../types/auth.types';
import logoImage from '@/shared/assets/images/logo/logo.png';

/**
 * Verify forgot password page component
 */
const VerifyForgotPasswordPage: React.FC = () => {
  const { t } = useTranslation(['auth', 'authValidation']);
  const navigate = useNavigate();
  const { verifyToken, verifyExpiresAt, setUserAuth } = useAuthCommon();
  const [otp, setOtp] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isOtpExpired, setIsOtpExpired] = useState<boolean>(false);
  const [isOtpVerified, setIsOtpVerified] = useState<boolean>(false);
  const [changePasswordToken, setChangePasswordToken] = useState<string | null>(null);

  // Sử dụng hook để xác thực OTP quên mật khẩu
  const { mutate: verifyForgotPassword, isPending: isVerifying } = useVerifyForgotPassword();

  // Sử dụng hook để đặt lại mật khẩu
  const { mutate: resetPassword, isPending: isResetting } = useResetPassword();

  // Sử dụng hook để gửi lại OTP
  const { mutate: resendOtp, isPending: isResending } = useResendOtp();

  // Kiểm tra nếu không có verifyToken, chuyển hướng về trang quên mật khẩu
  useEffect(() => {
    if (!verifyToken) {
      navigate('/auth/forgot-password');
    }
  }, [verifyToken, navigate]);

  // Kiểm tra thời gian hết hạn của OTP
  useEffect(() => {
    if (verifyExpiresAt) {
      console.log('verifyExpiresAt:', verifyExpiresAt, 'Current Time:', Date.now());
      const checkExpiration = () => {
        const now = Date.now();
        if (now >= verifyExpiresAt) {
          setIsOtpExpired(true);
        } else {
          setIsOtpExpired(false);
        }
      };

      // Kiểm tra ngay lập tức
      checkExpiration();

      // Kiểm tra mỗi giây
      const timer = setInterval(checkExpiration, 1000);

      return () => clearInterval(timer);
    }
    return undefined;
  }, [verifyExpiresAt]);

  // Không cần schema cho OTP vì chúng ta sử dụng component OTPInput

  // Schema cho form đặt lại mật khẩu
  const passwordSchema = z
    .object({
      newPassword: z
        .string()
        .min(1, t('authValidation:required', { field: t('auth:newPassword') }))
        .min(8, t('authValidation:minLength', { field: t('auth:newPassword'), length: 8 }))
        .regex(/[A-Z]/, t('authValidation:passwordUppercase'))
        .regex(/[a-z]/, t('authValidation:passwordLowercase'))
        .regex(/[0-9]/, t('authValidation:passwordNumber'))
        .regex(/[^A-Za-z0-9]/, t('authValidation:passwordSpecial')),
      confirmPassword: z
        .string()
        .min(1, t('authValidation:required', { field: t('auth:confirmPassword') })),
    })
    .refine(data => data.newPassword === data.confirmPassword, {
      message: t('authValidation:passwordsMatch'),
      path: ['confirmPassword'],
    });

  // Hàm xác thực OTP
  const handleVerifyOtp = (otpValue?: string) => {
    if (!verifyToken) return;

    // Sử dụng otpValue từ tham số hoặc state otp hiện tại
    const currentOtp = otpValue || otp;

    if (!currentOtp || currentOtp.length !== 6) {
      setError(t('auth:invalidOtp'));
      return;
    }

    setError(null);

    // Gọi API xác thực OTP quên mật khẩu
    verifyForgotPassword(
      {
        otpToken: verifyToken,
        otp: currentOtp,
      },
      {
        onSuccess: response => {
          if (response.code === 200) {
            // Lưu token đổi mật khẩu
            setChangePasswordToken(response.result.changePasswordToken);
            setIsOtpVerified(true);
          } else {
            setError(response.message || t('auth:verifyOtpError'));
          }
        },
        onError: error => {
          console.error('Verify OTP error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth:verifyOtpError');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setError(errorMsg);
        },
      }
    );
  };

  // Wrapper function cho button click
  const handleButtonClick = () => {
    handleVerifyOtp();
  };

  // Hàm gửi lại mã OTP
  const handleResendOtp = () => {
    if (!verifyToken) return;
    setError(null);

    // Gọi API gửi lại OTP
    resendOtp(
      {
        otpToken: verifyToken,
      },
      {
        onSuccess: response => {
          if (response.code === 200) {
            // Cập nhật thông tin xác thực trong Redux store
            // Lưu ý: Cần cập nhật lại verifyToken và expiresAt từ response
            // Điều này sẽ được xử lý bởi component cha
          } else {
            setError(response.message || t('auth:resendOtpError'));
          }
        },
        onError: error => {
          console.error('Resend OTP error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth:resendOtpError');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setError(errorMsg);
        },
      }
    );
  };

  // Hàm đặt lại mật khẩu
  const handleResetPassword = (values: unknown) => {
    if (!changePasswordToken) return;

    // Use type assertion with a specific type instead of 'any'
    const passwordValues = values as z.infer<typeof passwordSchema>;

    setError(null);

    // Gọi API đặt lại mật khẩu
    resetPassword(
      {
        token: changePasswordToken,
        password: passwordValues.newPassword,
        confirmPassword: passwordValues.confirmPassword,
      },
      {
        onSuccess: response => {
          if (response.code === 200) {
            // Lưu thông tin đăng nhập như phần login
            if (
              response.result &&
              typeof response.result === 'object' &&
              'accessToken' in response.result
            ) {
              const result = response.result as {
                accessToken?: string;
                expiresIn?: number;
                expiresAt?: number;
                user?: unknown;
              };

              if (result.accessToken) {
                // Ưu tiên sử dụng expiresAt từ API
                const expiresAt = result.expiresAt || 0;
                // Tính toán expiresIn từ expiresAt để tương thích với code cũ
                const expiresIn = Math.floor((expiresAt - Date.now()) / 1000);

                console.log('Reset password success - API response data:', {
                  hasExpiresAt: !!result.expiresAt,
                  expiresAt: result.expiresAt,
                  formattedExpiresAt: result.expiresAt ? new Date(result.expiresAt).toLocaleString() : 'N/A',
                  calculatedExpiresIn: expiresIn
                });

                // Lưu thông tin đăng nhập vào Redux
                setUserAuth({
                  accessToken: result.accessToken,
                  expiresIn: expiresIn,
                  expiresAt: expiresAt,
                  user: result.user as User,
                });

                console.log('Reset password success - token saved, navigating to home');

                // Navigate to home page
                navigate('/');
              }
            } else {
              // Fallback: nếu không có token trong response, chuyển về trang đăng nhập
              navigate('/auth', { state: { message: t('auth:resetPasswordSuccess') } });
            }
          } else {
            setError(response.message || t('auth:resetPasswordError'));
          }
        },
        onError: error => {
          console.error('Reset password error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth:resetPasswordError');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setError(errorMsg);
        },
      }
    );
  };

  return (
    <Card variant="elevated" className="w-full max-w-md">
      <div className="flex flex-col items-center mb-">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-10">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-8 object-contain max-w-[120px]"
          />
        </div>
      </div>

      <div className="space-y-6">
        <div className="text-center">
          <Typography variant="h4" className="mb-2">
            {t('auth:resetPassword')}
          </Typography>
          <Typography variant="body2" color="muted">
            {isOtpVerified ? t('auth:newPasswordDescription') : t('auth:resetPasswordDescription')}
          </Typography>
        </div>

        {error && <Alert type="error" message={error} className="mb-4" />}

        {/* Hiển thị đồng hồ đếm ngược nếu chưa xác thực OTP */}
        {!isOtpVerified && verifyExpiresAt && (
          <CountdownTimer
            expiresAt={verifyExpiresAt}
            onExpire={() => setIsOtpExpired(true)}
            className="mb-4 text-center"
          />
        )}

        {/* Form nhập OTP */}
        {!isOtpVerified && (
          <div className="space-y-6">
            <OTPInput
              length={6}
              onChange={setOtp}
              onComplete={handleVerifyOtp}
              disabled={isOtpExpired}
              className="mb-4"
            />

            <Button
              type="button"
              variant="primary"
              fullWidth
              onClick={handleButtonClick}
              isLoading={isVerifying}
              disabled={otp.length !== 6 || isOtpExpired}
            >
              {t('auth:verifyOtp')}
            </Button>

            <div className="text-center mt-4">
              <Typography variant="body2" color="muted">
                {t('auth:didntReceiveCode')}{' '}
                <a
                  href="#"
                  onClick={e => {
                    e.preventDefault();
                    handleResendOtp();
                  }}
                  className={`text-primary hover:text-primary-dark transition-colors ${isResending || !isOtpExpired ? 'pointer-events-none opacity-50' : ''}`}
                >
                  {t('auth:resendCode')}
                </a>
              </Typography>
            </div>
          </div>
        )}

        {/* Form đặt lại mật khẩu */}
        {isOtpVerified && (
          <Form schema={passwordSchema} onSubmit={handleResetPassword} className="space-y-4">
            <FormItem
              name="newPassword"
              label={t('auth:newPassword')}
              required
              helpText={t('auth:passwordRequirements')}
            >
              <PasswordInput
                fullWidth
              />
            </FormItem>

            <FormItem name="confirmPassword" label={t('auth:confirmPassword')} required>
              <PasswordInput
                fullWidth
              />
            </FormItem>

            <Button type="submit" variant="primary" fullWidth isLoading={isResetting}>
              {t('auth:resetPassword')}
            </Button>
          </Form>
        )}

        <div className="text-center">
          <Typography variant="body2" color="muted" className="mt-2">
            <Link to="/auth" className="text-primary hover:text-primary-dark transition-colors">
              {t('auth:backToLogin')}
            </Link>
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default VerifyForgotPasswordPage;
